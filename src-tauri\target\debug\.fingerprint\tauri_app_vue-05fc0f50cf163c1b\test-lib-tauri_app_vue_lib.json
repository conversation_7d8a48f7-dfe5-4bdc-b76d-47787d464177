{"rustc": 16591470773350601817, "features": "[]", "declared_features": "[]", "target": 5442004388195113429, "profile": 3316208278650011218, "path": 10763286916239946207, "deps": [[1556188385098558368, "simple_error", false, 12696118128540818018], [2706460456408817945, "futures", false, 1882469770815867224], [5746673292440776345, "env_logger", false, 6879364949692665640], [8606274917505247608, "tracing", false, 14224385343515587823], [9689903380558560274, "serde", false, 8054126872207136589], [9897246384292347999, "chrono", false, 13457460236878836661], [11862723254873017773, "bincode", false, 3011306223918538413], [12393800526703971956, "tokio", false, 6780345017345740994], [13625485746686963219, "anyhow", false, 10809945938079937536], [14039947826026167952, "tauri", false, 3278854557435629535], [15180317924524316486, "build_script_build", false, 3710860560945793403], [15367738274754116744, "serde_json", false, 614709112282052825], [15932120279885307830, "memchr", false, 5558816943422476298], [16230660778393187092, "tracing_subscriber", false, 10809885125657664636], [16257276029081467297, "serde_derive", false, 4515218308895454717], [16702348383442838006, "tauri_plugin_opener", false, 12425893593391307770], [17675327481376616781, "encoding", false, 10233107062496259603], [17874132307072864906, "time", false, 13243825817920007780], [17917672826516349275, "lazy_static", false, 1625160928766225417]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri_app_vue-05fc0f50cf163c1b\\dep-test-lib-tauri_app_vue_lib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}