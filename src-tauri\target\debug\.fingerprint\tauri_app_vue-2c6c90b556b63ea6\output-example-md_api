{"$message_type":"diagnostic","message":"unused imports: `io::Write` and `path::Path`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"examples\\md_api.rs","byte_start":14,"byte_end":23,"line_start":1,"line_end":1,"column_start":15,"column_end":24,"is_primary":true,"text":[{"text":"use std::{fs, io::Write, path::Path};","highlight_start":15,"highlight_end":24}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"examples\\md_api.rs","byte_start":25,"byte_end":35,"line_start":1,"line_end":1,"column_start":26,"column_end":36,"is_primary":true,"text":[{"text":"use std::{fs, io::Write, path::Path};","highlight_start":26,"highlight_end":36}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"examples\\md_api.rs","byte_start":12,"byte_end":35,"line_start":1,"line_end":1,"column_start":13,"column_end":36,"is_primary":true,"text":[{"text":"use std::{fs, io::Write, path::Path};","highlight_start":13,"highlight_end":36}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"examples\\md_api.rs","byte_start":9,"byte_end":10,"line_start":1,"line_end":1,"column_start":10,"column_end":11,"is_primary":true,"text":[{"text":"use std::{fs, io::Write, path::Path};","highlight_start":10,"highlight_end":11}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"examples\\md_api.rs","byte_start":35,"byte_end":36,"line_start":1,"line_end":1,"column_start":36,"column_end":37,"is_primary":true,"text":[{"text":"use std::{fs, io::Write, path::Path};","highlight_start":36,"highlight_end":37}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `io::Write` and `path::Path`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mexamples\\md_api.rs:1:15\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m1\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0muse std::{fs, io::Write, path::Path};\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m               \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `ascii_cstr_to_str` and `gb18030_cstr_to_str`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"examples\\md_api.rs","byte_start":165,"byte_end":184,"line_start":4,"line_end":4,"column_start":99,"column_end":118,"is_primary":true,"text":[{"text":"    print_rsp_info, set_cstr_from_str_truncate_i8, CThostFtdcReqUserLoginField, CtpAccountConfig, gb18030_cstr_to_str, trading_day_from_ctp_trading_day, ascii_cstr_to_str, ascii_cstr_to_str_i8,","highlight_start":99,"highlight_end":118}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"examples\\md_api.rs","byte_start":220,"byte_end":237,"line_start":4,"line_end":4,"column_start":154,"column_end":171,"is_primary":true,"text":[{"text":"    print_rsp_info, set_cstr_from_str_truncate_i8, CThostFtdcReqUserLoginField, CtpAccountConfig, gb18030_cstr_to_str, trading_day_from_ctp_trading_day, ascii_cstr_to_str, ascii_cstr_to_str_i8,","highlight_start":154,"highlight_end":171}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the unused imports","code":null,"level":"help","spans":[{"file_name":"examples\\md_api.rs","byte_start":163,"byte_end":184,"line_start":4,"line_end":4,"column_start":97,"column_end":118,"is_primary":true,"text":[{"text":"    print_rsp_info, set_cstr_from_str_truncate_i8, CThostFtdcReqUserLoginField, CtpAccountConfig, gb18030_cstr_to_str, trading_day_from_ctp_trading_day, ascii_cstr_to_str, ascii_cstr_to_str_i8,","highlight_start":97,"highlight_end":118}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null},{"file_name":"examples\\md_api.rs","byte_start":218,"byte_end":237,"line_start":4,"line_end":4,"column_start":152,"column_end":171,"is_primary":true,"text":[{"text":"    print_rsp_info, set_cstr_from_str_truncate_i8, CThostFtdcReqUserLoginField, CtpAccountConfig, gb18030_cstr_to_str, trading_day_from_ctp_trading_day, ascii_cstr_to_str, ascii_cstr_to_str_i8,","highlight_start":152,"highlight_end":171}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused imports: `ascii_cstr_to_str` and `gb18030_cstr_to_str`\u001b[0m\n\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mexamples\\md_api.rs:4:99\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m4\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m...\u001b[0m\u001b[0mrLoginField, CtpAccountConfig, gb18030_cstr_to_str, trading_day_from_ctp_trading_day, ascii_cstr_to_str, ascii_cstr_to_str_i8,\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                                   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m                                    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `auth_code`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"examples\\md_api.rs","byte_start":1517,"byte_end":1526,"line_start":45,"line_end":45,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"    let auth_code = ca.auth_code.as_str();","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_variables)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"examples\\md_api.rs","byte_start":1517,"byte_end":1526,"line_start":45,"line_end":45,"column_start":9,"column_end":18,"is_primary":true,"text":[{"text":"    let auth_code = ca.auth_code.as_str();","highlight_start":9,"highlight_end":18}],"label":null,"suggested_replacement":"_auth_code","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `auth_code`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mexamples\\md_api.rs:45:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m45\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let auth_code = ca.auth_code.as_str();\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_auth_code`\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(unused_variables)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `user_product_info`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"examples\\md_api.rs","byte_start":1561,"byte_end":1578,"line_start":46,"line_end":46,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"    let user_product_info = ca.user_product_info.as_str();","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"examples\\md_api.rs","byte_start":1561,"byte_end":1578,"line_start":46,"line_end":46,"column_start":9,"column_end":26,"is_primary":true,"text":[{"text":"    let user_product_info = ca.user_product_info.as_str();","highlight_start":9,"highlight_end":26}],"label":null,"suggested_replacement":"_user_product_info","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `user_product_info`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mexamples\\md_api.rs:46:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m46\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let user_product_info = ca.user_product_info.as_str();\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_user_product_info`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `app_id`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"examples\\md_api.rs","byte_start":1621,"byte_end":1627,"line_start":47,"line_end":47,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"    let app_id = ca.app_id.as_str();","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"examples\\md_api.rs","byte_start":1621,"byte_end":1627,"line_start":47,"line_end":47,"column_start":9,"column_end":15,"is_primary":true,"text":[{"text":"    let app_id = ca.app_id.as_str();","highlight_start":9,"highlight_end":15}],"label":null,"suggested_replacement":"_app_id","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `app_id`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mexamples\\md_api.rs:47:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m47\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    let app_id = ca.app_id.as_str();\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_app_id`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `u`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"examples\\md_api.rs","byte_start":3241,"byte_end":3242,"line_start":85,"line_end":85,"column_start":25,"column_end":26,"is_primary":true,"text":[{"text":"                    let u = p.p_rsp_user_login.unwrap();","highlight_start":25,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"examples\\md_api.rs","byte_start":3241,"byte_end":3242,"line_start":85,"line_end":85,"column_start":25,"column_end":26,"is_primary":true,"text":[{"text":"                    let u = p.p_rsp_user_login.unwrap();","highlight_start":25,"highlight_end":26}],"label":null,"suggested_replacement":"_u","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `u`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mexamples\\md_api.rs:85:25\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m85\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    let u = p.p_rsp_user_login.unwrap();\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_u`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused variable: `i`","code":{"code":"unused_variables","explanation":null},"level":"warning","spans":[{"file_name":"examples\\md_api.rs","byte_start":3395,"byte_end":3396,"line_start":88,"line_end":88,"column_start":25,"column_end":26,"is_primary":true,"text":[{"text":"                    for i in 0..12 {","highlight_start":25,"highlight_end":26}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"if this is intentional, prefix it with an underscore","code":null,"level":"help","spans":[{"file_name":"examples\\md_api.rs","byte_start":3395,"byte_end":3396,"line_start":88,"line_end":88,"column_start":25,"column_end":26,"is_primary":true,"text":[{"text":"                    for i in 0..12 {","highlight_start":25,"highlight_end":26}],"label":null,"suggested_replacement":"_i","suggestion_applicability":"MaybeIncorrect","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: unused variable: `i`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0mexamples\\md_api.rs:88:25\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m88\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m                    for i in 0..12 {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m                         \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11mhelp: if this is intentional, prefix it with an underscore: `_i`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"7 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 7 warnings emitted\u001b[0m\n\n"}
